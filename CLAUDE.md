# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

- **Development**: `npm run dev` - Start development server
- **Build**: `npm run build` - Create production build  
- **Preview**: `npm run preview` - Preview production build
- **Type checking**: `npm run check` - Run Svelte type checking
- **Linting**: `npm run lint` - Run ESLint and Prettier checks
- **Formatting**: `npm run format` - Format code with Prettier

## Project Architecture

This is a **SvelteKit application** for DSAT16 (SAT test preparation platform) using:

- **Frontend**: Svelte 5 with runes (`$state`, `$derived`, `$props`)
- **Backend**: SvelteKit server-side with form actions
- **Validation**: Zod schemas with sveltekit-superforms
- **Security**: Cloudflare Turnstile integration
- **Styling**: Custom CSS with design system variables
- **Data**: Firebase config and MailerLite integration

### Key Architecture Patterns

**Form Handling**: Uses sveltekit-superforms with Zod validation. Forms are processed server-side with Turnstile verification:
```typescript
// Server-side validation pattern
const form = await superValidate(request, zod4(schema));
if (!form.valid) return fail(400, { form });
```

**Component System**: Centralized UI library in `src/lib/ui/` with consistent prop patterns:
- All components use `$props()` destructuring
- Consistent naming: `class: className`, `bind:value`, error handling
- Typography system with H1-H5 and P1-P3 components

**Styling Architecture**: Custom CSS design system with semantic color variables:
```css
--primary, --secondary, --charcoal, --tangerine, --rose, --aquamarine, --yellow, --purple
```

### File Structure

```
src/
├── lib/
│   ├── ui/           # Reusable component library
│   ├── firebase/     # Firebase configuration
│   ├── server/       # Server-side utilities (MailerLite)
│   ├── landingPage/  # Landing page specific components
│   └── schema.ts     # Zod validation schemas
├── routes/
│   ├── +layout.svelte
│   ├── +page.svelte        # Main registration form
│   └── +page.server.ts     # Form actions with Turnstile
└── app.html
```

## Key Implementation Details

**SAT Score Validation**: Uses Vietnamese error messages with specific SAT score constraints (400-1600, multiples of 10)

**Turnstile Integration**: Server-side validation with Cloudflare API for bot protection

**Form Flow**: Registration form → Turnstile validation → Success message display

**Responsive Design**: Mobile-first approach with specific breakpoints at 768px

## Development Guidelines

- Follow Svelte 5 conventions with runes
- Use TypeScript interfaces over types
- Vietnamese language for user-facing text and validation messages  
- Maintain accessibility with proper ARIA labels
- Server-side form processing with progressive enhancement
- Component props use consistent destructuring patterns