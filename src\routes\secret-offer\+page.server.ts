import { fail } from '@sveltejs/kit';
import { message, superValidate } from 'sveltekit-superforms';
import { zod4 } from 'sveltekit-superforms/adapters';
import { infoSchema, twoStepCheckOutSchema } from '$lib/schema';

export const load = async () => {
    const form = await superValidate(zod4(twoStepCheckOutSchema));
    return { form };
};

export const actions = {
    step1: async ({ request }) => {
        const form = await superValidate(request, zod4(infoSchema));

        if (!form.valid) {
            return fail(400, { form });
        }

        return message(form, 'Step 1 completed');
    },

    step2: async ({ request }) => {
        const form = await superValidate(request, zod4(twoStepCheckOutSchema));

        if (!form.valid) {
            return fail(400, { form });
        }
        
        return message(form, 'Bạn đã đăng ký thành công. <PERSON><PERSON><PERSON> tra email để xác nhận.');
    }
};