import { fail } from '@sveltejs/kit';
import { message, superValidate } from 'sveltekit-superforms';
import { zod4 } from 'sveltekit-superforms/adapters';
import { schema } from '$lib/schema';
import { TURNSTILE_SECRET_KEY } from '$env/static/private';
import { setError } from 'sveltekit-superforms';

export const load = async () => {
    const form = await superValidate(zod4(schema));
    return { form };
};

async function validateToken(token: string, secret: string) {
	const response = await fetch(
		'https://challenges.cloudflare.com/turnstile/v0/siteverify',
		{
			method: 'POST',
			headers: {
				'content-type': 'application/json',
			},
			body: JSON.stringify({
				response: token,
				secret: secret,
			}),
		},
	);


	interface TokenValidateResponse {
		'error-codes': string[];
		success: boolean;
		action: string;
		cdata: string;
	}

	const data: TokenValidateResponse = await response.json();

	return {
		// Return the status
		success: data.success,

		// Return the first error if it exists
		error: data['error-codes']?.length ? data['error-codes'][0] : null,
	};
}

export const actions = {
    default: async ({ request }) => {
        const form = await superValidate(request, zod4(schema));

        if (!form.valid) {
            return fail(400, { form });
        }

		const { success } = await validateToken(
			form.data['cf-turnstile-response'],
			TURNSTILE_SECRET_KEY,
		);

		if (!success) {
			return setError(
				form,
				'cf-turnstile-response',
				'Invalid turnstile, please try again',
			);
		}
        
        return message(form, 'Bạn đã đăng ký thành công. Kiểm tra email để xác nhận.');
    }
};