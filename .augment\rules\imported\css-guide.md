---
type: "agent_requested"
description: "Css guide"
---
    You are an expert developer in HTML and CSS, focusing on best practices, accessibility, and responsive design.

    Key Principles
    - Write semantic HTML to improve accessibility and SEO.
    - Use CSS for styling, avoiding inline styles.
    - Ensure responsive design using media queries and flexible layouts.
    - Prioritize accessibility by using ARIA roles and attributes.
    - Use neo-brutalism design.

    HTML
    - Use semantic elements (e.g., <header>, <main>, <footer>, <article>, <section>).
    - Use <button> for clickable elements, not <div> or <span>.
    - Use <a> for links, ensuring href attribute is present.
    - Use <img> with alt attribute for images.
    - Use <form> for forms, with appropriate input types and labels.
    - Avoid using deprecated elements (e.g., <font>, <center>).
    - Always reuse components, if possible. There are a lot of components in lib/ui.

    CSS
    - Do not use inline styles.
    - Use class selectors over ID selectors for styling.
    - Use Flexbox and Grid for layout.
    - Use rem and em units for scalable and accessible typography. Do not use px unless for media queries
    - Use CSS variables for consistent theming.
    - Use BEM (Block Element Modifier) methodology for naming classes.
    - Avoid !important; use specificity to manage styles.
    - For fonts, only uses Inter or Open Sans in most cases. In the simulations folder, you can also use Merriweather for text and Chivo for numbers.
    - Always use flex + gap instead of margin top/bottom
    - Indent CSS styles properly. Use 4-space (or 1-tab). Add spaces in between classes group.
    - Use these variable names for colors:
        --sky-blue: #66E2FF;
		--purple: #8C53FF;
		--aquamarine: #55ECB2;
		--yellow: #FFC800;
		--tangerine: #F68712;
		--rose: #EB47AB;
		--pitch-black: #000000;
		--very-light-sky-blue: #f5fdff;
		--light-sky-blue: #DAF9FF;
		--light-purple: #EEE5FF;
		--light-aquamarine: #D6FAED;
		--light-yellow: #FFF1C1;
		--light-tangerine: #FDE2C5;
		--light-rose: #FBDEF0;
		--white: #FFFFFF;
		--charcoal: #333333;

    Responsive Design
    - Use media queries to create responsive layouts.
    - Use mobile-first approach for media queries.
    - Ensure touch targets are large enough for touch devices.
    - Use responsive images with srcset and sizes attributes.
    - Use viewport meta tag for responsive scaling.
    - There are 3 main breakpoints: 540px, 768px, and 1024px. 

    Accessibility
    - Use ARIA roles and attributes to enhance accessibility.
    - Ensure sufficient color contrast for text.
    - Provide keyboard navigation for interactive elements.
    - Use focus styles to indicate focus state.
    - Use landmarks (e.g., <nav>, <main>, <aside>) for screen readers.

    Performance
    - Minimize CSS and HTML file sizes.
    - Use CSS minification and compression.
    - Avoid excessive use of animations and transitions.
    - Use lazy loading for images and other media.

    Testing
    - Test HTML and CSS in multiple browsers and devices.
    - Use tools like Lighthouse for performance and accessibility audits.
    - Validate HTML and CSS using W3C validators.

    Documentation
    - Comment complex CSS rules and HTML structures.
    - Use consistent naming conventions for classes and IDs.
    - Document responsive breakpoints and design decisions.

    Refer to MDN Web Docs for HTML and CSS best practices and to the W3C guidelines for accessibility standards.



    