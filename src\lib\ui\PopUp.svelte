
<script lang="ts">
	import type { Snippet } from 'svelte';

	interface Props {
		isOpen: boolean;
		children?: Snippet;
		size?: 'small' | 'medium' | 'large' | 'very-large';
		closeOnOutsideClick?: boolean;
		closeOnEscape?: boolean;
		showCloseButton?: boolean;
		onclose?: () => void;
	}

	let { 
		isOpen = $bindable(false),
		children,
		size = 'medium',
		closeOnOutsideClick = true,
		closeOnEscape = true,
		showCloseButton = true,
		onclose = undefined
	}: Props = $props();

	let dialogElement: HTMLElement | null = $state(null);

	// Close popup when clicking outside
	function handleOutsideClick(event: MouseEvent) {
		if (closeOnOutsideClick && event.target === dialogElement) {
			isOpen = false;
			onclose?.();
		}
	}

	// Close popup on escape key
	function handleKeydown(event: KeyboardEvent) {
		if (closeOnEscape && event.key === 'Escape') {
			isOpen = false;
			onclose?.();
		}
	}

	// Handle close button click
	function closePopup() {
		isOpen = false;
		onclose?.();
	}

	// Prevent scrolling when popup is open
	$effect(() => {
		if (isOpen) {
			document.body.style.overflow = 'hidden';
		} else {
			document.body.style.overflow = '';
		}

		// Cleanup on component destroy
		return () => {
			document.body.style.overflow = '';
		};
	});
</script>

<svelte:window onkeydown={handleKeydown} />

{#if isOpen}
	<!-- svelte-ignore a11y_click_events_have_key_events -->
	<!-- svelte-ignore a11y_no_noninteractive_element_interactions -->
	<dialog 
		bind:this={dialogElement}
		class="popup-overlay"
		onclick={handleOutsideClick}
		aria-modal="true"
	>
		<div class="popup-content popup-{size}">
			<div class="popup-header">
				{#if showCloseButton}
					<button 
						class="close-button" 
						onclick={closePopup}
						aria-label="Close popup"
						type="button"
					>
						<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
							<path d="M18 6L6 18M6 6L18 18" stroke="var(--close-button-color, --pitch-black)" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
						</svg>
					</button>
				{/if}
			</div>
			
			<div class="popup-body">
				{@render children?.()}
			</div>
		</div>
	</dialog>
{/if}

<style>
	.popup-overlay {
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background-color: rgba(0, 0, 0, 0.5);
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 1000;
		margin: 0;
		padding: 1rem;
		border: none;
		animation: fadeIn 0.3s ease-out;
	}

	.popup-content {
		background-color: var(--popup-color, var(--white));
		border-radius: 0.75rem;
		box-shadow: 0.25rem 0.25rem 0 var(--pitch-black);
		max-height: 90vh;
		overflow-y: auto;
		border: 1px solid var(--pitch-black);
		padding: 2rem;
		text-align: center;
	}

	.popup-small {
		width: 100%;
		max-width: 400px;
	}

	.popup-medium {
		width: 100%;
		max-width: 600px;
	}

	.popup-large {
		width: 100%;
		max-width: 800px;
	}

	.popup-very-large {
		width: 100%;
		max-width: 1200px;
	}

	.popup-header {
		display: flex;
		align-items: center;
		justify-content: flex-end;
	}

	.popup-title {
		margin: 0;
		padding-bottom: 1rem;
		font-size: 1.5rem;
		font-weight: 600;
		color: var(--pitch-black);
	}

	.close-button {
		background: none;
		border: none;
		cursor: pointer;
		padding: 0.5rem;
		border-radius: 6px;
		color: var(--charcoal);
		transition: all 0.2s ease;
		display: flex;
		align-items: center;
		justify-content: center;
		min-width: 44px;
		min-height: 44px;
	}

	.popup-body {
		display: flex;
		flex-direction: column;
		gap: 1rem;
		align-items: center;
		text-wrap: pretty;
	}

	/* Mobile responsiveness */
	@media (max-width: 640px) {
		.popup-overlay {
			padding: 0.5rem;
		}
		
		.popup-content {
			border-radius: 8px;
			padding: 1rem;
		}

		.close-button {
			padding: 0;
			min-width: 32px;
			min-height: 32px;
		}
		
		.popup-title {
			font-size: 1.25rem;
		}
		
		.popup-small,
		.popup-medium,
		.popup-large {
			max-width: none;
		}
	}

	/* Ensure proper scrolling on touch devices */
	@media (max-height: 600px) {
		.popup-content {
			max-height: 85vh;
		}
	}

	/* High contrast mode support */
	@media (prefers-contrast: high) {
		.popup-content {
			border: 3px solid var(--pitch-black);
		}
		
		.popup-header {
			border-bottom-color: var(--pitch-black);
		}
	}

	/* Reduced motion support */
	@media (prefers-reduced-motion: reduce) {
		.popup-overlay,
		.popup-content {
			animation: none;
		}
	}
</style>
