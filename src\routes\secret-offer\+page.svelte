<script lang="ts">
    import TwoStepForm from '../../lib/ui/TwoStepForm.svelte';
    import { Button, H1, H2, H3, P1, P2 } from '$lib/ui';

    let { data } = $props();
</script>

<svelte:head>
	<title>DSAT16 - Special Offer!</title>
</svelte:head>

<div class="webpage">
    <!-- Header Section -->
    <div class="header-section">
        <H1>DSAT16 Offer Đặc Biệt!</H1>
        <div class="limited-time">
            <H2>Dành riêng cho người tham gia webinar</H2>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <div class="form-and-table">
            <!-- Left Side - Order Form -->
            <div id="order-form" class="order-form">
                <TwoStepForm {data} />
            </div>

            <div class="video-and-table">

            </div>
        </div>
        
        <!-- Right Side - Product Info -->
        <div class="product-info">
            {@render productTable()}
            {@render valueStack()}
        </div>
    </div>
</div>

{#snippet productTable()}
    <div class="product-table">
        <H3 --text-align="center">Tất cả mọi thứ trong gói</H3>
        <div class="table-container">
            <div class="table-row">
                <P1>DSAT16 (1 năm)</P1>
                <P1 --text-color="var(--rose)">Trị giá 4.992.000đ</P1>
            </div>
            <div class="table-row">
                <P1>DSAT16 Bootcamp (1 năm)</P1>
                <P1 --text-color="var(--rose)">Trị giá 1.160.000đ</P1>
            </div>
            <div class="table-row">
                <P1>Meeting với Tutor 1600</P1>
                <P1 --text-color="var(--rose)">Trị giá 5.616.000đ</P1>
            </div>
            <div class="table-row">
                <P1>Bonus: 67 đề thi SAT official</P1>
                <P1 --text-color="var(--rose)">Vô giá!</P1>
            </div>
            <div class="table-row">
                <P1>14 ngày hoàn tiền 100%</P1>
                <P1 --text-color="var(--rose)">Không rủi ro!</P1>
            </div>
        </div>
        <H3 --text-align="center">Tổng giá trị: <s style:color=var(--rose)>12.768.000đ</s></H3>
        <H3 --text-align="center">Mua trong hôm nay: <span style:color=var(--purple)>5.160.000đ</span></H3>
    </div>
{/snippet}

{#snippet valueStack()}
    <div class="value-stack">
        <H2 --text-align="center">Tất cả mọi thứ bạn sẽ được truy cập ngay lập tức nếu mua luôn trong hôm nay</H2>

        <div class="value-items">
            <div class="value-item">
                <H3>ClickFunnels: 6 Month Platinum Account</H3>
                <P1>Get your funnels built in as little as 10 minutes, using our SIMPLE "drag n drop" editor!</P1>
                <P2>No tech or coding experience required!</P2>
                <div class="value-price">$1,782 Value</div>
            </div>

            <div class="value-item">
                <H3>Funnel Hacking Secrets Masterclass</H3>
                <P1>Module 1 - Funnel Hacking Secrets</P1>
                <P1>Module 2 - Lead Funnels... Get our Squeeze Page, Survey, Summit, and Brick & Mortar Funnel templates</P1>
                <P1>Module 3 - Unboxing Funnels... Get our Book, Cart, Challenge, Membership, and Supplement Funnel templates!</P1>
                <div class="value-price">$1,997 Value</div>
            </div>

            <div class="value-item">
                <H3>Traffic Secrets Course</H3>
                <P1>Get ALL Of My BEST Traffic-Driving Strategies, Including:</P1>
                <P2>My solo ad secrets… My media buying secrets... My Facebook traffic secrets…</P2>
                <div class="value-price">$1,997 Value</div>
            </div>

            <div class="value-item">
                <H3>Daily Virtual Hack-a-thon</H3>
                <P1>Get your funnel built, in ONE Hackathon session!</P1>
                <P2>Watch and follow along as our experts build all sorts of different funnels step-by-step</P2>
                <div class="value-price">$5,776 Value</div>
            </div>
        </div>

        <div class="total-value">
            <H3>Total Value: $11,552</H3>
            <H2>Get It Today For Only: $997</H2>
            <a href="#order-form">
                <Button fullWidth>Yes! I Want Funnel Hacking Secrets Now!</Button>
            </a>
        </div>
    </div>
{/snippet}

<style>
    .webpage {
        width: 100%;
        font-size: 1rem;
        -webkit-tap-highlight-color: transparent;
        background: var(--white);
        min-height: 100vh;
    }

    .header-section {
        text-align: center;
        padding: 2rem;
        background: var(--sky-blue);
        border-bottom: 0.25rem solid var(--pitch-black);
    }

    .limited-time {
        margin-top: 1rem;
        padding: 1rem;
        background: var(--yellow);
        border: 0.125rem solid var(--pitch-black);
        border-radius: 0.5rem;
        display: inline-block;
    }

    .main-content {
        display: flex;
        flex-direction: column;
        gap: 2rem;
        max-width: 90rem;
        margin: 0 auto;
        padding: 2rem;
    }

    .form-and-table {
        flex: 1;
        display: flex;
        gap: 2rem;
    }

    .product-info {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 2rem;
    }

    .order-form {
        background: var(--light-aquamarine);
        border: 0.25rem solid var(--pitch-black);
        border-radius: 1rem;
        box-shadow: 0.5rem 0.5rem 0 var(--pitch-black);
        height: fit-content;
        flex: 1 1 0;
    }

    .video-and-table {
        flex: 2 1 0;
    }

    .product-table {
        background: var(--white);
        border: 0.25rem solid var(--pitch-black);
        border-radius: 1rem;
        padding: 1.5rem;
        box-shadow: 0.25rem 0.25rem 0 var(--pitch-black);
        height: fit-content;
    }

    .table-container {
        margin: 1rem 0;
        border: 3px solid var(--pitch-black);
    }

    .table-row {
        display: flex;
        justify-content: space-between;
        padding: 0.75rem;
        border-bottom: 0.125rem solid var(--pitch-black);
        background: var(--very-light-sky-blue);
    }

    .table-row:last-child {
        border-bottom: none;
        border-radius: 0 0 0.5rem 0.5rem;
    }

    .value-stack {
        background: var(--light-purple);
        border: 0.25rem solid var(--pitch-black);
        border-radius: 1rem;
        padding: 2rem;
        box-shadow: 0.25rem 0.25rem 0 var(--pitch-black);
    }

    .value-items {
        display: flex;
        flex-direction: column;
        gap: 1.5rem;
        margin: 2rem 0;
    }

    .value-item {
        background: var(--white);
        border: 0.125rem solid var(--pitch-black);
        border-radius: 0.75rem;
        padding: 1.5rem;
        box-shadow: 0.125rem 0.125rem 0 var(--pitch-black);
    }

    .value-price {
        font-weight: 600;
        color: var(--purple);
        font-size: 1.125rem;
        margin-top: 0.5rem;
    }

    .total-value {
        text-align: center;
        background: var(--yellow);
        border: 0.25rem solid var(--pitch-black);
        border-radius: 1rem;
        padding: 2rem;
        box-shadow: 0.25rem 0.25rem 0 var(--pitch-black);
    }
</style>