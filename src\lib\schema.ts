import { z } from 'zod/v4';
import valid from 'card-validator';

const zodSATScore = z.int().min(400, '<PERSON><PERSON><PERSON><PERSON> SAT phải ít nhất 400').max(1600, '<PERSON><PERSON><PERSON><PERSON> SAT phải tối đa 1600').multipleOf(10, '<PERSON><PERSON><PERSON><PERSON> SAT phải là bội số của 10');
export const zodSATDates = ["04-10-2025", "08-11-2025", "06-12-2025", "14-03-2026", "02-05-2026", "06-06-2026", "Chưa xác định"];

export const schema = z.object({
    name: z.string().min(1, 'Tên là bắt buộc'),
    email: z.email('Email không hợp lệ'),
    grade: z.int().min(1, 'Lớp là bắt buộc').max(13, 'Lớp phải tối đa là 13').default(NaN),
    test_date: z.enum(zodSATDates),
    current_sat: zodSATScore.default(400),
    target_sat: zodSATScore.default(1600),
    'cf-turnstile-response': z.string().nonempty('Please complete captcha')
});

export const infoSchema = z.object({
    name: z.string().min(1, 'Tên là bắt buộc'),
    email: z.email('Email không hợp lệ'),
    phone: z.e164('Điện thoại không hợp lệ'),
    // address: z.string().min(1, 'Địa chỉ là bắt buộc'),
    // city: z.string().min(1, 'Thành phố là bắt buộc'),
});

export const paymentSchema = z.object({
    card_number: z.custom((val) => valid.number(val).isValid, 'Số thẻ không hợp lệ').and(z.number().min(1, 'Số thẻ là bắt buộc')),
    expiration_date: z.custom((val) => valid.expirationDate(val).isValid, 'Ngày hết hạn không hợp lệ').and(z.string().min(1, 'Ngày hết hạn là bắt buộc')),
    cvv: z.custom((val) => valid.cvv(val).isValid, 'CVV không hợp lệ').and(z.number().min(1, 'CVV là bắt buộc')),
    cardholder_name: z.custom((val) => valid.cardholderName(val).isValid, 'Tên trên thẻ không hợp lệ').and(z.string().min(1, 'Tên trên thẻ là bắt buộc')),
});

export const twoStepCheckOutSchema = z.object({
    ...infoSchema.shape,
    ...paymentSchema.shape
});

export type InfoSchema = typeof infoSchema;
export type PaymentSchema = typeof paymentSchema;
export type TwoStepCheckOutSchema = typeof twoStepCheckOutSchema;